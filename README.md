# EasyExpress - DHL-like Package Tracking System

A modern package tracking and logistics management system built with Next.js, Nest.js, and MongoDB.

## 🚀 Features

- **Package Tracking**: Real-time package tracking with status updates
- **User Management**: Customer registration and authentication
- **Blog System**: Content management for company news and updates
- **Service Pages**: Information about shipping services
- **Contact System**: Customer inquiry and support forms
- **Admin Dashboard**: Package and user management interface

## 🛠️ Tech Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **React Hook Form** - Form handling and validation
- **Axios** - HTTP client for API calls

### Backend
- **Nest.js** - Progressive Node.js framework
- **MongoDB** - NoSQL database
- **Mongoose** - MongoDB object modeling
- **JWT** - Authentication and authorization
- **Class Validator** - Request validation
- **Swagger** - API documentation

## 📁 Project Structure

```
EasyExpress/
├── frontend/          # Next.js frontend application
├── backend/           # Nest.js backend API
├── docs/             # Project documentation
└── README.md         # This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- MongoDB 6+
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd EasyExpress
```

2. Install dependencies for both frontend and backend
```bash
# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

3. Set up environment variables
```bash
# Backend environment
cp backend/.env.example backend/.env

# Frontend environment  
cp frontend/.env.example frontend/.env.local
```

4. Start MongoDB service

5. Run the applications
```bash
# Start backend (from backend directory)
npm run start:dev

# Start frontend (from frontend directory)
npm run dev
```

## 📖 Documentation

- [API Documentation](./docs/api.md)
- [Frontend Setup](./frontend/README.md)
- [Backend Setup](./backend/README.md)
- [Database Schema](./docs/database-schema.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License.
