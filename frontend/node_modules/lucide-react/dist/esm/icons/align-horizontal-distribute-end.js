/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "6", height: "14", x: "4", y: "5", rx: "2", key: "1wwnby" }],
  ["rect", { width: "6", height: "10", x: "14", y: "7", rx: "2", key: "1fe6j6" }],
  ["path", { d: "M10 2v20", key: "uyc634" }],
  ["path", { d: "M20 2v20", key: "1tx262" }]
];
const AlignHorizontalDistributeEnd = createLucideIcon(
  "align-horizontal-distribute-end",
  __iconNode
);

export { __iconNode, AlignHorizontalDistributeEnd as default };
//# sourceMappingURL=align-horizontal-distribute-end.js.map
