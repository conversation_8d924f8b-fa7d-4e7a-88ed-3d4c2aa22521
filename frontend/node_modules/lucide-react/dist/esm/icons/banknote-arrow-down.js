/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5", key: "x6cv4u" }],
  ["path", { d: "m16 19 3 3 3-3", key: "1ibux0" }],
  ["path", { d: "M18 12h.01", key: "yjnet6" }],
  ["path", { d: "M19 16v6", key: "tddt3s" }],
  ["path", { d: "M6 12h.01", key: "c2rlol" }],
  ["circle", { cx: "12", cy: "12", r: "2", key: "1c9p78" }]
];
const BanknoteArrowDown = createLucideIcon("banknote-arrow-down", __iconNode);

export { __iconNode, BanknoteArrowDown as default };
//# sourceMappingURL=banknote-arrow-down.js.map
