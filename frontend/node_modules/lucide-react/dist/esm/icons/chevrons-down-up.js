/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m7 20 5-5 5 5", key: "13a0gw" }],
  ["path", { d: "m7 4 5 5 5-5", key: "1kwcof" }]
];
const ChevronsDownUp = createLucideIcon("chevrons-down-up", __iconNode);

export { __iconNode, ChevronsDownUp as default };
//# sourceMappingURL=chevrons-down-up.js.map
