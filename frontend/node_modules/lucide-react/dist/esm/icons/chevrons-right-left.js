/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m20 17-5-5 5-5", key: "30x0n2" }],
  ["path", { d: "m4 17 5-5-5-5", key: "16spf4" }]
];
const ChevronsRightLeft = createLucideIcon("chevrons-right-left", __iconNode);

export { __iconNode, ChevronsRightLeft as default };
//# sourceMappingURL=chevrons-right-left.js.map
